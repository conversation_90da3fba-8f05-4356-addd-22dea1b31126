{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Yarp": "Information",
      "AgentWebApi.Gateway": "Debug"
    }
  },
  
  // YARP Gateway Configuration - YARP网关配置
  "ReverseProxy": {
    "Routes": {
      "ai-agent-api": {
        "ClusterId": "ai-agent-cluster",
        "Match": {
          "Path": "/api/{**catch-all}"
        }
      },
      "semantic-kernel-route": {
        "ClusterId": "semantic-kernel-cluster",
        "Match": {
          "Path": "/sk/{**catch-all}"
        },
        "Transforms": [
          { "PathPattern": "/api/semantickernel/{**catch-all}" }
        ]
      },
      "rag-route": {
        "ClusterId": "rag-cluster",
        "Match": {
          "Path": "/rag/{**catch-all}"
        },
        "Transforms": [
          { "PathPattern": "/api/rag/{**catch-all}" }
        ]
      },
      "chromadb-route": {
        "ClusterId": "chromadb-cluster",
        "Match": {
          "Path": "/chromadb/{**catch-all}"
        },
        "Transforms": [
          { "PathPattern": "/api/chromadb/{**catch-all}" }
        ]
      },
      "signalr-route": {
        "ClusterId": "signalr-cluster",
        "Match": {
          "Path": "/hubs/{**catch-all}"
        }
      }
    },
    "Clusters": {
      "ai-agent-cluster": {
        "LoadBalancingPolicy": "RoundRobin",
        "Destinations": {
          "ai-agent-1": {
            "Address": "https://localhost:5001/"
          },
          "ai-agent-2": {
            "Address": "https://localhost:5002/"
          }
        },
        "HealthCheck": {
          "Active": {
            "Enabled": true,
            "Interval": "00:00:30",
            "Timeout": "00:00:05",
            "Policy": "ConsecutiveFailures",
            "Path": "/health"
          },
          "Passive": {
            "Enabled": true,
            "Policy": "TransportFailureRate",
            "ReactivationPeriod": "00:02:00"
          }
        }
      },
      "semantic-kernel-cluster": {
        "LoadBalancingPolicy": "LeastRequests",
        "Destinations": {
          "sk-service-1": {
            "Address": "https://localhost:5001/"
          }
        }
      },
      "rag-cluster": {
        "LoadBalancingPolicy": "PowerOfTwoChoices",
        "Destinations": {
          "rag-service-1": {
            "Address": "https://localhost:5001/"
          }
        }
      },
      "chromadb-cluster": {
        "LoadBalancingPolicy": "RoundRobin",
        "Destinations": {
          "chromadb-1": {
            "Address": "http://localhost:8000/"
          }
        }
      },
      "signalr-cluster": {
        "LoadBalancingPolicy": "RoundRobin",
        "SessionAffinity": {
          "Enabled": true,
          "Policy": "Cookie",
          "AffinityKeyName": "ai-agent-signalr"
        },
        "Destinations": {
          "signalr-1": {
            "Address": "https://localhost:5001/"
          }
        }
      }
    }
  },
  
  // Circuit Breaker Configuration - 熔断器配置
  "CircuitBreaker": {
    "Enabled": true,
    "DefaultFailureRatio": 0.5,
    "DefaultMinimumThroughput": 10,
    "DefaultSamplingDuration": "00:00:30",
    "DefaultBreakDuration": "00:01:00"
  },
  
  // Python.NET Configuration - Python.NET配置
  "Python": {
    "PythonPath": "/usr/bin/python3",
    "VirtualEnvironmentPath": "~/.ai-agent/venv",
    "FinetuneScriptPath": "../finetune/examples/simple_finetune.py",
    "MaxConcurrentJobs": 2,
    "DefaultTimeout": "01:00:00"
  },
  
  // Workflow Configuration - 工作流配置
  "Workflow": {
    "DefaultWorkingDirectory": "~/.ai-agent/workflows",
    "MaxPlansInMemory": 100,
    "AutoSaveInterval": "00:05:00",
    "EnableFileWatcher": true
  },
  
  // Sandbox Terminal Configuration - 沙盒终端配置
  "SandboxTerminal": {
    "MaxConcurrentSessions": 10,
    "SessionTimeout": "00:30:00",
    "EnableSafetyChecks": true,
    "AllowedCommands": ["ls", "pwd", "echo", "cat", "grep", "find", "ps", "top", "df", "free"],
    "BlockedPatterns": ["rm -rf", "sudo", "su", "chmod 777", ":(){ :|:& };:"]
  }
}

