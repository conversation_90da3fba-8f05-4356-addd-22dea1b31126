
#!/usr/local/bin/bpftrace

/*
 * process_monitor.bt
 * 监控特定进程活动的bpftrace脚本
 * This bpftrace script monitors activity of a specific process.
 */

BEGIN
{
    printf("Monitoring process ", comm, "... Hit Ctrl-C to end.\n");
}

// 跟踪进程启动
// Trace process start
tracepoint:sched:sched_process_exec
/comm == comm/
{
    printf("Process Exec: PID %d, Comm %s, Filename %s\n", pid, comm, args->filename);
}

// 跟踪进程退出
// Trace process exit
tracepoint:sched:sched_process_exit
/comm == comm/
{
    printf("Process Exit: PID %d, Comm %s, Exit Code %d\n", pid, comm, args->exit_code);
}

// 跟踪文件打开
// Trace file open
kprobe:do_sys_openat2
/comm == comm/
{
    printf("File Open: PID %d, Comm %s, Filename %s\n", pid, comm, str(arg1));
}

// 跟踪文件写入
// Trace file write
kprobe:vfs_write
/comm == comm/
{
    printf("File Write: PID %d, Comm %s, Size %d\n", pid, comm, arg2);
}

// 跟踪内存分配
// Trace memory allocation
kprobe:__kmalloc
/comm == comm/
{
    printf("Memory Alloc: PID %d, Comm %s, Size %d\n", pid, comm, arg0);
}

END
{
    printf("Process monitoring ended.\n");
}


