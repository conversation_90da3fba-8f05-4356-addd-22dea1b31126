<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.16" />
    <PackageReference Include="ModelContextProtocol" Version="0.1.0-preview.14" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    
    <!-- YARP Gateway and Circuit Breaker -->
    <PackageReference Include="Yarp.ReverseProxy" Version="2.1.0" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="8.0.0" />
    <PackageReference Include="Polly" Version="8.2.0" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    
    <!-- IdentityServer4 and Authentication SDKs -->
    <PackageReference Include="IdentityServer4" Version="4.1.2" />
    <PackageReference Include="IdentityServer4.AspNetIdentity" Version="4.1.2" />
    <PackageReference Include="IdentityServer4.EntityFramework" Version="4.1.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    
    <!-- SignalR SDK for Real-time Communication -->
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.0" />
    
    <!-- Python.NET SDK for Python integration -->
    <!-- Python.NET SDK用于Python集成 -->
    <PackageReference Include="Python.Runtime" Version="3.0.3" />
    <PackageReference Include="Python.Included" Version="3.11.4" />
    
    <!-- PostgreSQL and EF Core SDK -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
    
    <!-- ChromaDB SDK -->
    <PackageReference Include="ChromaDB.Client" Version="1.0.0" />
    
    <!-- Microsoft Semantic Kernel -->
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.57.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Connectors.OpenAI" Version="1.57.0" />
    <PackageReference Include="Microsoft.SemanticKernel.Plugins.Memory" Version="1.57.0-alpha" />
    
    <!-- OpenTelemetry SDK -->
    <PackageReference Include="OpenTelemetry" Version="1.7.0" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.7.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.7.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.7.0" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.7.0" />
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.7.0" />
    
    <!-- Dapr SDK -->
    <PackageReference Include="Dapr.AspNetCore" Version="1.12.0" />
    <PackageReference Include="Dapr.Client" Version="1.12.0" />
    <PackageReference Include="Dapr.Extensions.Configuration" Version="1.12.0" />
  </ItemGroup>

</Project>
