#!/bin/bash
# run_model_server.sh
# Script to run the Qwen3-4B model server

set -e  # Exit immediately if a command exits with a non-zero status

echo "Starting Qwen3-4B model server on port 2025..."

# Check if virtual environment exists and activate it if it does
if [ -d "/home/<USER>/ai-agent/venv/bin" ]; then
    echo "Activating virtual environment..."
    source /home/<USER>/ai-agent/venv/bin/activate
else
    echo "No virtual environment found, using system Python..."
fi

# Run the model server
cd /home/<USER>/ai-agent
python3 src/model_server.py

# Note: The server will continue running until manually stopped
