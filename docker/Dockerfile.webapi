# Dockerfile for .NET Web API
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["AgentWebApi/AgentWebApi.csproj", "AgentWebApi/"]
RUN dotnet restore "AgentWebApi/AgentWebApi.csproj"
COPY . .
WORKDIR "/src/AgentWebApi"
RUN dotnet build "AgentWebApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "AgentWebApi.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "AgentWebApi.dll"]
