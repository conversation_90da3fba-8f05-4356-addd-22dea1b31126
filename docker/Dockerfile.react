# Dockerfile for React UI
FROM node:20-alpine AS build

# Set working directory
WORKDIR /app

# Copy package.json and pnpm-lock.yaml
COPY AgentUI/agent-chat/package.json AgentUI/agent-chat/pnpm-lock.yaml ./

# Install pnpm
RUN npm install -g pnpm

# Install dependencies
RUN pnpm install

# Copy the rest of the application
COPY AgentUI/agent-chat/ ./

# Build the application
RUN pnpm run build

# Production stage
FROM nginx:alpine AS final
COPY --from=build /app/dist /usr/share/nginx/html
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
