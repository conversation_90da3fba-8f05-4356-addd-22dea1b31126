version: '3.8'

services:
  # ClickHouse service
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    container_name: ai-agent-clickhouse
    ports:
      - "8123:8123"   # HTTP interface
      - "9000:9000"   # Native interface
    volumes:
      - clickhouse-data:/var/lib/clickhouse
      - clickhouse-logs:/var/log/clickhouse-server
    environment:
      - CLICKHOUSE_USER=default
      - CLICKHOUSE_PASSWORD=clickhouse_password
      - CLICKHOUSE_DB=ai_agent_analytics
    ulimits:
      nofile:
        soft: 262144
        hard: 262144
    restart: unless-stopped
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://localhost:8123/ping || exit 1
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    networks:
      - ai-agent-data-network

  # Elasticsearch service
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.10.4
    container_name: ai-agent-elasticsearch
    environment:
      - node.name=es01
      - cluster.name=ai-agent-es-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"   # REST API
      - "9300:9300"   # Node communication
    restart: unless-stopped
    healthcheck:
      test: curl -s http://localhost:9200/_cluster/health | grep -q '"status":"green"\|"status":"yellow"' || exit 1
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ai-agent-data-network

  # Kibana service (optional, for Elasticsearch visualization)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.10.4
    container_name: ai-agent-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - ai-agent-data-network

networks:
  ai-agent-data-network:
    driver: bridge

volumes:
  clickhouse-data:
  clickhouse-logs:
  elasticsearch-data:
